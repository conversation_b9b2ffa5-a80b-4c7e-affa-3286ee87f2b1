"use client";

import React, { useState, useTransition, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
import { SupplierFormValues, EnhancedSupplierSchema } from "./types";
import SupplierFormTabs from "./components/SupplierFormTabs";
import { ArrowLeft, Check, AlertCircle } from "lucide-react";
import SupplierSummary from "./components/SupplierSummary";
import { addSupplier } from "@/actions/entities/suppliers";
import { useContactLimits } from "@/hooks/useSubscriptionLimits";

const EnhancedSupplierPage: React.FC = () => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const {
    canCreateContact,
    contactMessage,
    currentContactUsage,
    contactLimit,
    isLoading: limitsLoading,
  } = useContactLimits();

  // Initialize the form with enhanced schema
  const form = useForm<SupplierFormValues>({
    resolver: zodResolver(EnhancedSupplierSchema),
    defaultValues: {
      name: "",
      firstName: "",
      middleName: "",
      lastName: "",
      contactName: "",
      phone: "",
      telephone: "",
      fax: "",
      email: "",
      identityType: "",
      identityNumber: "",
      NIK: "",
      NPWP: "",
      companyName: "",
      otherInfo: "",
      address: "",
      billingAddress: "",
      shippingAddress: "",
      sameAsShipping: false,
      bankName: "",
      bankBranch: "",
      accountHolder: "",
      accountNumber: "",
      notes: "",
      website: "",
      taxId: "",
      paymentTerms: "",
      supplierType: "product",
      isActive: true,
      rating: 0,
      socialMedia: {
        facebook: "",
        instagram: "",
        twitter: "",
        linkedin: "",
      },
      bankInfo: {
        bankName: "",
        accountNumber: "",
        accountName: "",
      },
      tags: [],
    },
  });

  // Watch form values for the summary component
  const formValues = form.watch();

  // Check subscription limits on component mount
  useEffect(() => {
    if (!limitsLoading && !canCreateContact) {
      toast.error(contactMessage || "Batas kontak tercapai untuk paket Anda.");
      router.push("/dashboard/suppliers");
    }
  }, [canCreateContact, contactMessage, limitsLoading, router]);

  // Handle form submission
  const onSubmit = (values: SupplierFormValues) => {
    // Double-check subscription limits before submission
    if (!canCreateContact) {
      toast.error(contactMessage || "Batas kontak tercapai untuk paket Anda.");
      return;
    }

    startTransition(async () => {
      // Show loading toast with ID so we can dismiss it later
      const toastId = toast.loading("Menyimpan data supplier...");

      try {
        // Extract only the fields that are in the SupplierSchema for the addSupplier action
        const supplierData = {
          name: values.name,
          firstName: values.firstName,
          middleName: values.middleName,
          lastName: values.lastName,
          contactName: values.contactName,
          phone: values.phone,
          telephone: values.telephone,
          fax: values.fax,
          email: values.email,
          identityType: values.identityType,
          identityNumber: values.identityNumber,
          NIK: values.NIK,
          NPWP: values.NPWP,
          companyName: values.companyName,
          otherInfo: values.otherInfo,
          address: values.address,
          billingAddress: values.billingAddress,
          shippingAddress: values.shippingAddress,
          sameAsShipping: values.sameAsShipping,
          bankName: values.bankName || values.bankInfo?.bankName,
          bankBranch: values.bankBranch,
          accountHolder: values.bankInfo?.accountName,
          accountNumber: values.bankInfo?.accountNumber || values.accountNumber,
          notes: values.notes,
        };

        const result = await addSupplier(supplierData);

        // Dismiss the loading toast
        toast.dismiss(toastId);

        if (result.success) {
          toast.success(result.success);
          form.reset(); // Reset form on success
          // Redirect after a short delay
          router.push("/dashboard/suppliers");
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        // Dismiss any loading toasts in case of error
        toast.dismiss();
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  return (
    <div className="space-y-6">
      {/* Header with back button */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" size="sm" className="gap-1" asChild>
          <Link href="/dashboard/suppliers">
            <ArrowLeft className="h-4 w-4" />
            Kembali ke Daftar Supplier
          </Link>
        </Button>
        <Button
          type="submit"
          form="supplier-form"
          disabled={isPending}
          className="gap-1"
        >
          <Check className="h-4 w-4" />
          {isPending ? "Menyimpan..." : "Simpan Supplier"}
        </Button>
      </div>

      <Form {...form}>
        <form id="supplier-form" onSubmit={form.handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Form Content */}
            <div className="lg:col-span-2">
              <SupplierFormTabs control={form.control} isPending={isPending} />
            </div>

            {/* Summary Sidebar */}
            <div className="lg:col-span-1">
              <SupplierSummary formValues={formValues} isPending={isPending} />
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default EnhancedSupplierPage;
