import React from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Filter,
  Plus,
  Search,
  SlidersHorizontal,
  Download,
  Upload,
  Lock,
} from "lucide-react";
import { ColumnVisibility } from "../types";
import { customersColumnConfig } from "../config/columnConfig";
import { CustomerImportExport } from "./CustomerImportExport";
import { useContactLimits } from "@/hooks/useSubscriptionLimits";
import { toast } from "sonner";

interface CustomerActionsProps {
  columnVisibility: ColumnVisibility;
  setColumnVisibility: React.Dispatch<React.SetStateAction<ColumnVisibility>>;
  searchTerm: string;
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
  onFilterClick: () => void;
  onImportClick: () => void;
  onExportClick: () => void;
  onRefresh?: () => void; // Add refresh callback for after import
}

export const CustomerActions: React.FC<CustomerActionsProps> = ({
  columnVisibility,
  setColumnVisibility,
  searchTerm,
  setSearchTerm,
  onFilterClick,
  onImportClick,
  onExportClick,
  onRefresh,
}) => {
  const {
    canCreateContact,
    contactMessage,
    currentContactUsage,
    contactLimit,
    isLoading: limitsLoading,
  } = useContactLimits();

  const handleAddCustomerClick = (e: React.MouseEvent) => {
    if (!canCreateContact) {
      e.preventDefault();
      toast.error(contactMessage || "Batas kontak tercapai untuk paket Anda.");
      return;
    }
  };
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      {/* Search */}
      <div className="relative w-full sm:w-64">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
        <Input
          type="search"
          placeholder="Cari pelanggan..."
          className="w-full pl-8"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* Actions */}
      <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
        {/* Filter Button */}
        <Button
          variant="outline"
          size="sm"
          className="h-9"
          onClick={onFilterClick}
        >
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>

        {/* Column Visibility */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-9">
              <SlidersHorizontal className="mr-2 h-4 w-4" />
              Kolom
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>Tampilkan Kolom</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {customersColumnConfig.map((column) => (
              <DropdownMenuCheckboxItem
                key={column.key}
                checked={columnVisibility[column.key]}
                onCheckedChange={(checked) =>
                  setColumnVisibility((prev) => ({
                    ...prev,
                    [column.key]: !!checked,
                  }))
                }
                onSelect={(e) => e.preventDefault()}
              >
                {column.label}
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Import/Export Component */}
        <CustomerImportExport onRefresh={onRefresh} />

        {/* Add Customer Button */}
        {canCreateContact ? (
          <Link
            href="/dashboard/customers/new"
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer"
          >
            <Plus className="mr-2 h-5 w-5" />
            Tambah
          </Link>
        ) : (
          <Button
            disabled
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-gray-400 px-4 py-2 text-sm font-medium text-white shadow-sm cursor-not-allowed"
            onClick={handleAddCustomerClick}
            title={contactMessage || "Batas kontak tercapai"}
          >
            <Lock className="mr-2 h-5 w-5" />
            Tambah
          </Button>
        )}
      </div>
    </div>
  );
};
